import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './components/Layout/ThemeContext';
import LoginPage from './pages/Login/LoginPage';
import RegisterPage from './pages/Register/RegisterPage';
import ForgotPasswordPage from './pages/ForgotPassword/ForgotPasswordPage';
import Dashboard from './pages/Dashboard/Dashboard';
import AdminDashboard from './pages/AdminDashboard/AdminDashboard';
import VideoPlay from './pages/CourseVideo/VideoPlay';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';

// TEST: SAMPLE PLAYLIST DATA FOR DEVELOPMENT
// TODO: Replace with real course data from database when YouTube credentials are available
// This data simulates YouTube playlists for testing the UI without requiring API keys
const samplePlaylists = [
  {
    // TEST: Using a real public YouTube playlist ID for demonstration
    id: 'PL4cUxeGkcC9gZD-Tvwfod2gaISzfRiP9', // Traversy Media React Tutorial Playlist
    thumbnail: 'https://i.ytimg.com/vi/hQAHSlTtcmY/hqdefault.jpg', // First video thumbnail
    videoCount: 12,
    name: 'TEST - React Tutorial Course',
    metadata: 'YouTube Playlist - Development Sample',
    description: 'Complete React tutorial series - TEST data for development without YouTube credentials',
    channelTitle: 'Traversy Media'
  },
  {
    // TEST: Sample course data for art classes
    id: 'PL2',
    thumbnail: 'https://via.placeholder.com/320x180/FF6B6B/FFFFFF?text=Pintura+1',
    videoCount: 12,
    name: 'TEST - Introducción a la Pintura al Óleo',
    metadata: 'Actualizado hace 2 días - Sample Data',
    description: 'Aprende los fundamentos de la pintura al óleo desde cero - TEST data',
    channelTitle: 'Zona Cero Art'
  }
];

function AppRoutes() {
  const { isAdmin } = useAuth();

  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route element={<ProtectedRoute />}>
        <Route
          path="/dashboard"
          element={isAdmin ? <AdminDashboard /> : <Dashboard courses={samplePlaylists} />}
        />
        <Route path="/course-video/:id" element={<VideoPlay />} />
      </Route>
      <Route path="/" element={<LoginPage />} />
    </Routes>
  );
}

function App() {
  useEffect(() => {
    const handleContextMenu = (e) => {
      e.preventDefault();
    };

    document.addEventListener('contextmenu', handleContextMenu);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <AppRoutes />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;