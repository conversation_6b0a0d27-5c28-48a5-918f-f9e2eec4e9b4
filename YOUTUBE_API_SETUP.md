# YouTube API Integration Setup Guide

This guide explains how to set up YouTube API integration for the Zona Cero platform, allowing admins to link their YouTube channels and import playlists as courses.

## Overview

The YouTube API integration allows:
- **Admin users** to configure YouTube API credentials
- **Sync playlists** from YouTube channels as courses on the platform
- **Students** to watch YouTube videos directly within the platform
- **Access to unlisted/hidden videos** that only the channel owner can view

## Prerequisites

1. **Google Cloud Console Account** - You need a Google account to access the Google Cloud Console
2. **YouTube Channel** - The admin must own or have access to the YouTube channel
3. **YouTube API Key** - Required for basic API access
4. **OAuth 2.0 Credentials** (optional but recommended) - For accessing private/unlisted videos

## Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3:
   - Go to "APIs & Services" > "Library"
   - Search for "YouTube Data API v3"
   - Click "Enable"

## Step 2: Create API Credentials

### Option A: API Key Only (for public content)
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API key"
3. Copy the generated API key
4. (Optional) Restrict the API key to specific APIs and referrers

### Option B: OAuth 2.0 (for private/unlisted content)
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure OAuth consent screen if prompted
4. Select "Web application" as application type
5. Add authorized redirect URIs (can be localhost for development)
6. Copy the Client ID and Client Secret

## Step 3: Configure YouTube Credentials in the Platform

1. **Login as Admin** - Access the admin dashboard
2. **Navigate to YouTube Settings** - Click the "YouTube" tab
3. **Configure API Key** (required):
   - Enter your YouTube API Key
   - Click "Guardar Credenciales"
   - Click "Probar Conexión" to verify

4. **Configure OAuth (optional, for private videos)**:
   - Enter Client ID and Client Secret
   - You'll need to obtain a Refresh Token separately (see below)

## Step 4: Obtain OAuth Refresh Token (for private videos)

For accessing private/unlisted videos, you need an OAuth refresh token:

### Using OAuth Playground (recommended):
1. Go to [Google OAuth 2.0 Playground](https://developers.google.com/oauthplayground/)
2. Click the gear icon (settings)
3. Enter your OAuth Client ID and Client Secret
4. Select "YouTube Data API v3" from the list
5. Click "Authorize APIs"
6. Login with the YouTube channel owner's account
7. Grant permissions
8. Click "Exchange authorization code for tokens"
9. Copy the "Refresh token" value
10. Paste it into the platform's "Refresh Token" field

## Step 5: Sync YouTube Playlists as Courses

1. **Load Playlists** - In the admin dashboard, go to "YouTube" > "Sincronizar Playlists"
2. **Click "Cargar Playlists"** - This fetches all playlists from your channel
3. **Select and Sync** - Click "Sincronizar" on any playlist you want to import as a course
4. **Customize (optional)** - You can modify the title and description before syncing

## Step 6: Assign Courses to Students

1. **Go to Students Tab** - In the admin dashboard
2. **Manage Course Access** - Click "Gestionar Cursos" for any student
3. **Grant Access** - Toggle the courses you want students to access

## How It Works

### For Public Videos:
- Only API key is required
- Videos must be public or unlisted
- Students can access videos through the embedded YouTube player

### For Private/Unlisted Videos:
- OAuth credentials are required
- Videos can be completely hidden from public YouTube
- Only authenticated users (channel owner) can access through the API
- Students see videos embedded in the platform interface

### Video Playback:
- Videos are embedded using YouTube's iframe player
- Students can watch videos directly within the platform
- Playlist sidebar shows all videos in the course
- Clicking any video in the sidebar switches to that video

## Database Schema

The integration adds these fields to the database:

### Users Table (YouTube credentials):
- `youtube_channel_id` - Channel identifier
- `youtube_api_key` - API key for basic access
- `youtube_client_id` - OAuth client ID
- `youtube_client_secret` - OAuth client secret
- `youtube_refresh_token` - OAuth refresh token
- `youtube_access_token` - Current access token
- `youtube_token_expiry` - Token expiration time

### Courses Table (YouTube integration):
- `youtube_playlist_id` - YouTube playlist ID
- `youtube_playlist_url` - Full playlist URL
- `youtube_channel_title` - Channel name
- `youtube_video_count` - Number of videos
- `youtube_thumbnail_url` - Playlist thumbnail
- `synced_at` - Last sync timestamp

## API Endpoints

### Admin Endpoints:
- `GET /api/admin/youtube/channel` - Get channel info
- `GET /api/admin/youtube/playlists` - Get channel playlists
- `POST /api/admin/youtube/sync-playlist` - Sync playlist as course
- `GET /api/admin/courses/:courseId/videos` - Get course videos
- `PUT /api/admin/youtube/credentials` - Update YouTube credentials
- `GET /api/admin/youtube/credentials` - Get current credentials

### User Endpoints:
- `GET /api/my/courses/:courseId/videos` - Get enrolled course videos

## Security Considerations

1. **API Key Security**:
   - Store API keys securely (not in client-side code)
   - Use environment variables in production
   - Restrict API keys to specific domains/referrers

2. **OAuth Tokens**:
   - Refresh tokens have long lifespans
   - Store securely and rotate periodically
   - Never expose to client-side code

3. **Video Access**:
   - Private videos are only accessible through authenticated API calls
   - Students cannot access videos directly on YouTube
   - Platform controls all video access

## Troubleshooting

### Common Issues:

1. **"YouTube API key not configured"**
   - Ensure you've entered the API key in admin settings
   - Verify the API key is valid and not restricted

2. **"Could not determine YouTube channel ID"**
   - Make sure you're logged in with the correct Google account
   - Verify the channel exists and is accessible

3. **"Error syncing playlist"**
   - Check playlist privacy settings
   - Ensure OAuth credentials are correct for private playlists

4. **Videos not loading**
   - Verify internet connection
   - Check if videos are region-restricted
   - Ensure playlist still exists on YouTube

### Debug Steps:
1. Test API connection in admin dashboard
2. Check browser console for errors
3. Verify credentials are saved correctly
4. Test with a public playlist first

## Development Notes

The integration is designed to be:
- **Modular** - YouTube functionality is separated from core platform features
- **Secure** - Credentials are stored server-side only
- **Scalable** - Can handle multiple admin channels
- **Maintainable** - Clean separation of concerns

### Key Files:
- `backend/youtube-service.js` - YouTube API integration logic
- `backend/migrations/add-youtube-credentials.sql` - Database schema
- `src/pages/AdminDashboard/YouTubeSettings.jsx` - Admin UI
- `src/pages/CourseVideo/VideoPlay.jsx` - Video player interface

## Support

For issues with YouTube API integration:
1. Check Google Cloud Console for API quota/limits
2. Verify all credentials are correctly configured
3. Test with Google's API Explorer first
4. Check YouTube API documentation for specific errors

---

**Note**: This integration requires active YouTube API quotas and proper credential management. Monitor your API usage in Google Cloud Console to avoid unexpected charges.
