import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';
import {
  DashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  CourseFilter,
  MainContent,
  CourseItem,
  ImgFrame,
  VideoCountBadge,
  CourseInfo,
  CourseName,
  PlaylistMetadata,
  LoadingText,
  ErrorText,
  NoResultsText
} from './Dashboard.styles';

const Dashboard = ({ courses = [] }) => {
  const navigate = useNavigate();
  const [playlists, setPlaylists] = useState([]);
  const [loading, setLoading] = useState(false); // Set to false to show sample data immediately
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Handle search functionality
  const handleSearch = async (query) => {
    if (!query.trim()) {
      setError(null);
      return;
    }

    try {
      setLoading(true);
      // Filter current courses/playlists
      const currentData = playlists.length > 0 ? playlists : courses;
      const filteredResults = currentData.filter(course =>
        course.name?.toLowerCase().includes(query.toLowerCase())
      );
      setPlaylists(filteredResults);
      setError(null);
    } catch (err) {
      console.error('Search failed:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Use API data when available, fallback to courses prop
  const displayCourses = playlists.length > 0 ? playlists : courses;

  return (
    <DashboardContainer>
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Mis Cursos</MainTitle>
          <CourseFilter
            name="course-filter"
            id="course-filter"
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
          >
            <option value="all">Todos los cursos</option>
            <option value="playlist">Pintura</option>
            <option value="course">Dibujo</option>
          </CourseFilter>
        </MainHeader>
        <MainContent>
          {loading && <LoadingText>Cargando playlists...</LoadingText>}
          {error && <ErrorText>Error: {error}</ErrorText>}
          {!loading && !error && displayCourses.length === 0 && (
            <NoResultsText>No se encontraron playlists</NoResultsText>
          )}
          {!loading && !error && displayCourses.map((course, index) => (
            <CourseItem
              key={course.id || index}
              onClick={() => navigate(`/course-video/${course.id || index}`)}
            >
              <ImgFrame>
                {course.thumbnail && (
                  <img src={course.thumbnail} alt={course.name} />
                )}
                {course.videoCount && (
                  <VideoCountBadge>{course.videoCount} videos</VideoCountBadge>
                )}
              </ImgFrame>
              <CourseInfo>
                <CourseName>{course.name}</CourseName>
                <PlaylistMetadata>{course.metadata}</PlaylistMetadata>
              </CourseInfo>
            </CourseItem>
          ))}
        </MainContent>
      </Main>
      <Footer />
    </DashboardContainer>
  );
};

export default Dashboard;
